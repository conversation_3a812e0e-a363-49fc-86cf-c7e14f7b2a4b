import pdfplumber
import textwrap
import os
import json
import random
import string

def generate_placeholder_string(table_data):
    """
    Generate a random alphanumeric string with the same character count as the table content.
    This ensures proper token representation without problematic characters.
    
    Args:
        table_data (list): Table data as a list of lists
        
    Returns:
        str: Random string with same length as concatenated table content
    """
    if not table_data or not any(table_data):
        return ""
    
    # Calculate total character count from table
    total_chars = 0
    for row in table_data:
        for cell in row:
            if cell is not None:
                cell_str = str(cell).strip()
                if cell_str:
                    total_chars += len(cell_str)
    
    # Generate random string of same length
    # Use alphanumeric characters to avoid any special character issues
    chars = string.ascii_letters + string.digits
    placeholder = ''.join(random.choice(chars) for _ in range(total_chars))
    
    return placeholder

def process_pdf_to_txt(input_pdf_path, output_txt_path, tables_dir=None):
    """
    Read a PDF file and write its content to a TXT file while extracting tables separately.
    Tables are stored as separate files and replaced with random placeholder strings of equivalent length.
    
    Args:
        input_pdf_path (str): Path to input PDF file
        output_txt_path (str): Path to output TXT file
        tables_dir (str): Directory to store extracted tables (optional)
    """
    if tables_dir is None:
        base_name = os.path.splitext(os.path.basename(output_txt_path))[0]
        tables_dir = os.path.join(os.path.dirname(output_txt_path), f"{base_name}_tables")
    
    os.makedirs(tables_dir, exist_ok=True)
    
    extracted_tables = {}
    table_counter = 1
    written_placeholders = {}  # Track placeholders written to file
    
    # Set random seed for reproducibility (optional - remove for true randomness)
    random.seed(42)
    
    with pdfplumber.open(input_pdf_path) as pdf:
        with open(output_txt_path, 'w', encoding='utf-8') as txt_file:
            prev_page_table = None
            prev_page_headers = None
            prev_page_placeholder = None
            
            for page_num, page in enumerate(pdf.pages):
                print(f"Processing page {page_num + 1}...")
                table_areas = page.find_tables()
                
                if table_areas:
                    print(f"  Found {len(table_areas)} table areas on page {page_num + 1}")
                    tables = page.extract_tables()
                    
                    table_bbox_areas = []
                    for table_area in table_areas:
                        table_bbox_areas.append({
                            'top': table_area.bbox[1],
                            'bottom': table_area.bbox[3],
                            'x0': table_area.bbox[0],
                            'x1': table_area.bbox[2]
                        })
                    
                    table_bbox_areas.sort(key=lambda t: t['top'])
                    
                    current_y = 0
                    page_height = page.height
                    
                    for idx, table_pos in enumerate(table_bbox_areas):
                        if current_y < table_pos['top']:
                            crop_area = (
                                0,                  # x0
                                current_y,          # top
                                page.width,         # x1
                                table_pos['top']    # bottom
                            )
                            
                            above_text = page.crop(crop_area).extract_text()
                            if above_text:
                                txt_file.write(above_text + "\n\n")
                        
                        table_index = table_bbox_areas.index(table_pos)
                        table_data = tables[table_index]
                        
                        is_continuation = False
                        current_table_id = None
                        placeholder_string = None
                        formatted_table = None
                        write_placeholder = True  # Flag to control whether to write placeholder
                        
                        if idx == 0 and prev_page_table is not None and table_pos['top'] < 100:
                            if table_data and len(table_data[0]) == len(prev_page_headers):
                                is_continuation = True
                                
                                current_table_id = prev_page_table['id']
                                print(f"  Continuing table {current_table_id} from previous page")
                                
                                # Combine with previous page data
                                full_table = prev_page_table['data'] + table_data
                                placeholder_string = generate_placeholder_string(full_table)
                                formatted_table = format_table(full_table, is_continued=True)
                                
                                # Update the mapping with the complete table
                                extracted_tables[placeholder_string] = {
                                    'id': current_table_id,
                                    'formatted_content': formatted_table,
                                    'char_count': len(placeholder_string),
                                    'pages': prev_page_table.get('pages', []) + [page_num + 1],
                                    'position': prev_page_table.get('position', {})
                                }
                                
                                # Also keep the old placeholder mapping for compatibility
                                if prev_page_placeholder and prev_page_placeholder in written_placeholders:
                                    extracted_tables[prev_page_placeholder] = extracted_tables[placeholder_string].copy()
                                    print(f"  Keeping old placeholder mapping for {current_table_id}")
                                
                                print(f"  Updated continued table {current_table_id}")
                            else:
                                is_continuation = False
                                print(f"  Table structure mismatch, treating as new table")
                        
                        if not is_continuation:
                            current_table_id = f"table_{table_counter}"
                            print(f"  Creating new table {current_table_id}")
                            table_counter += 1
                            
                            placeholder_string = generate_placeholder_string(table_data)
                            formatted_table = format_table(table_data)
                            
                            # Check if this table might continue on next page
                            if table_pos['bottom'] > (page_height - 100):
                                # This table might continue, so we'll write placeholder but may update later
                                prev_page_table = {
                                    'id': current_table_id,
                                    'data': table_data,
                                    'pages': [page_num + 1],
                                    'position': {
                                        'page': page_num + 1,
                                        'top': table_pos['top'],
                                        'bottom': table_pos['bottom']
                                    }
                                }
                                prev_page_headers = table_data[0] if table_data else None
                                prev_page_placeholder = placeholder_string
                                print(f"  Table {current_table_id} extends to page bottom, may continue")
                            else:
                                prev_page_table = None
                                prev_page_headers = None
                                prev_page_placeholder = None
                            
                            # Always add to extracted_tables for now
                            extracted_tables[placeholder_string] = {
                                'id': current_table_id,
                                'formatted_content': formatted_table,
                                'char_count': len(placeholder_string),
                                'pages': [page_num + 1],
                                'position': {
                                    'page': page_num + 1,
                                    'top': table_pos['top'],
                                    'bottom': table_pos['bottom']
                                }
                            }
                        
                        # Write placeholder to file
                        if write_placeholder and placeholder_string:
                            table_placeholder = f"\n{{{placeholder_string}}}\n"
                            txt_file.write(table_placeholder + "\n")
                            written_placeholders[placeholder_string] = current_table_id
                            print(f"  Inserted placeholder for {current_table_id} (length: {len(placeholder_string)} chars)")
                        
                        current_y = table_pos['bottom']
                    
                    if current_y < page_height:
                        crop_area = (
                            0,              # x0
                            current_y,      # top
                            page.width,     # x1
                            page_height     # bottom
                        )
                        
                        below_text = page.crop(crop_area).extract_text()
                        if below_text:
                            txt_file.write(below_text + "\n\n")
                else:
                    print(f"  No tables found on page {page_num + 1}")
                    text = page.extract_text()
                    txt_file.write(text + "\n\n")

                    prev_page_table = None
                    prev_page_headers = None
                    prev_page_placeholder = None
    
    # Final cleanup: ensure all written placeholders are in the index
    print("\nVerifying all written placeholders are in index...")
    for placeholder, table_id in written_placeholders.items():
        if placeholder not in extracted_tables:
            print(f"  WARNING: Written placeholder for {table_id} not in final index!")
            # Try to find the table by ID and add the placeholder mapping
            for p, info in extracted_tables.items():
                if info['id'] == table_id:
                    extracted_tables[placeholder] = info.copy()
                    print(f"  Added missing placeholder mapping for {table_id}")
                    break
    
    save_extracted_tables(extracted_tables, tables_dir)
    
    create_table_index(extracted_tables, tables_dir)
    
    print(f"\nExtracted {len(set(info['id'] for info in extracted_tables.values()))} unique tables")
    print(f"Total placeholder mappings: {len(extracted_tables)}")
    print(f"Table IDs created: {sorted(set(info['id'] for info in extracted_tables.values()))}")
    return extracted_tables

def save_extracted_tables(extracted_tables, tables_dir):
    """
    Save each extracted table as a separate text file with formatted content.
    
    Args:
        extracted_tables (dict): Dictionary of extracted tables (placeholder -> metadata)
        tables_dir (str): Directory to save tables
    """
    for placeholder_string, table_info in extracted_tables.items():
        table_id = table_info['id']
        formatted_content = table_info['formatted_content']
        table_file_path = os.path.join(tables_dir, f"{table_id}.txt")
        with open(table_file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_content)

def create_table_index(extracted_tables, tables_dir):
    """
    Create an index file with metadata about all extracted tables.
    The mapping now uses placeholder strings as keys and metadata as values.
    
    Args:
        extracted_tables (dict): Dictionary of extracted tables (placeholder -> metadata)
        tables_dir (str): Directory to save the index
    """
    index_data = {}
    for placeholder_string, table_info in extracted_tables.items():
        table_id = table_info['id']
        # Store mapping from placeholder to file info
        index_data[placeholder_string] = {
            'id': table_id,
            'char_count': table_info['char_count'],
            'pages': table_info['pages'],
            'position': table_info['position'],
            'file': f"{table_id}.txt"
        }
    
    index_file_path = os.path.join(tables_dir, "table_index.json")
    with open(index_file_path, 'w', encoding='utf-8') as f:
        json.dump(index_data, f, indent=2)

def format_table(table_data, is_continued=False):
    """
    Format a table as ASCII text with borders, handling long cell content properly.
    
    Args:
        table_data (list): Table data as a list of lists
        is_continued (bool): Whether this table is a continuation from previous page
        
    Returns:
        str: Formatted table as ASCII text
    """
    if not table_data or not any(table_data):
        return ""
    
    cleaned_table = []
    for row in table_data:
        cleaned_row = []
        for cell in row:
            cell_str = str(cell).strip() if cell is not None else ""
            cell_str = cell_str.replace('\n', ' ').strip()
            cleaned_row.append(cell_str)
        cleaned_table.append(cleaned_row)
    
    if cleaned_table:
        header_row = cleaned_table[0]
        max_col_width = 40  # Maximum width for any column
        min_col_width = 10  # Minimum width for any column
        
        col_widths = []
        for cell in header_row:
            width = min(max(len(cell), min_col_width), max_col_width)
            col_widths.append(width)
    else:
        return ""
    
    output = []
    
    border = '+' + '+'.join('-' * (width + 2) for width in col_widths) + '+'
    
    if is_continued:
        output.append(border)
        continued_note = "| " + "TABLE CONTINUED FROM PREVIOUS PAGE".center(sum(col_widths) + len(col_widths)*3 - 4) + " |"
        output.append(continued_note)
    
    output.append(border)
    
    for row_idx, row in enumerate(cleaned_table):
        if is_continued and row_idx == 0:
            continue
            
        wrapped_cells = []
        
        for i, cell in enumerate(row):
            if i < len(col_widths):
                if len(cell) > col_widths[i]:
                    wrapped_text = textwrap.wrap(cell, col_widths[i])
                    wrapped_cells.append(wrapped_text)
                else:
                    wrapped_cells.append([cell])
        
        max_lines = max(len(cell_lines) for cell_lines in wrapped_cells) if wrapped_cells else 1
        
        for line_idx in range(max_lines):
            line_cells = []
            for i, cell_lines in enumerate(wrapped_cells):
                if line_idx < len(cell_lines):
                    text = cell_lines[line_idx]
                else:
                    text = ""
                
                line_cells.append(f" {text:<{col_widths[i]}} ")
            
            output.append('|' + '|'.join(line_cells) + '|')
        
        output.append(border)
    
    table_text = '\n'.join(output)
    
    return table_text

# Get the absolute path to the workspace root
workspace_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))

# Construct absolute paths for input and output files
pdf_path = "C:\\Users\\<USER>\\MedNova\\Missing Docs\\3. Clinical Plan\\wma-declaration-of-helsinki.pdf"
output_path = "C:\\Users\\<USER>\\MedNova\\Missing Docs\\3. Clinical Plan\\wma-declaration-of-helsinki.txt"

# Process the PDF with table extraction
extracted_tables = process_pdf_to_txt(pdf_path, output_path)