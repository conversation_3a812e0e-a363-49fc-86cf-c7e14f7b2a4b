+---------------+------------------------------------------+
| User Guidance | This data element identifies the sender  |
|               | of the ICH ICSR Acknowledgement batch    |
|               | file (creator of ICH ICSR                |
|               | Acknowledgement batch file).             |
+---------------+------------------------------------------+
| Conformance   | Required                                 |
+---------------+------------------------------------------+
| Data Type     | 60AN                                     |
+---------------+------------------------------------------+
| OID           | 2.16.840.1.113883.3.989.2.1.3.17         |
+---------------+------------------------------------------+
| Value Allowed | Free text                                |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | This should be the same identifier as    |
|               | N.1.4. The following notation will be    |
|               | used to represent ACK.M.2: <id           |
|               | extension="acknowledgement sender identi |
|               | fier"root="2.16.840.1.113883.3.989.2.1.3 |
|               | .17"/> The root indicates the namespace  |
|               | of ACK.M.2, the actual batch sender      |
|               | identifier is populated at id extension. |
|               | The sender identifier should be agreed   |
|               | between trading partners.                |
+---------------+------------------------------------------+