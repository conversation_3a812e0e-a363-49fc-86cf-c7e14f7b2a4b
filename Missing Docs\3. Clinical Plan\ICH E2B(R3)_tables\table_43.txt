+---------------+------------------------------------------+
| User Guidance | This data element has the function of a  |
|               | timestamp and represents the equivalent  |
|               | of a version number for the ICSR. Every  |
|               | ICSR and every iteration (e.g. version)  |
|               | of an ICSR in a safety message must have |
|               | a different value for Date of Creation.  |
|               | The most recent version of an ICSR will  |
|               | have the most recent date; previous      |
|               | versions of an ICSR will have older      |
|               | dates.                                   |
+---------------+------------------------------------------+
| Conformance   | Required                                 |
+---------------+------------------------------------------+
| Data Type     | Date / Time                              |
+---------------+------------------------------------------+
| OID           | None                                     |
+---------------+------------------------------------------+
| Value Allowed | See Appendix II for further information. |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | Minimum precision required is date and   |
|               | time to the second. The date specified   |
|               | cannot refer to a future date; the time  |
|               | zone may have to be specified. (i.e.     |
|               | ‘CCYYMMDDhhmmss[+/-ZZzz]’)               |
+---------------+------------------------------------------+