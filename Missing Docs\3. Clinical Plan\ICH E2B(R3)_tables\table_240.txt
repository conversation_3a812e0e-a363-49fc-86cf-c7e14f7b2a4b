+---------------+------------------------------------------+
| User Guidance | This data element captures the value     |
|               | (amount) for the test result.A qualifier |
|               | symbol can be added to the value when    |
|               | appropriate. The supported qualifiers    |
|               | are ‘greater than’, ‘less than’,         |
|               | ‘greater than or equal to’ and ‘less     |
|               | than or equal to’.                       |
+---------------+------------------------------------------+
| Conformance   | Optional, but required if F.r.2 is       |
|               | populated, and F.r.3.1 and F.r.3.4 is    |
|               | not populated.                           |
+---------------+------------------------------------------+
| Data Type     | 50N                                      |
+---------------+------------------------------------------+
| OID           | None                                     |
+---------------+------------------------------------------+
| Value Allowed | Numeric nullFlavor: NINF, PINF           |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | The symbols ‘>’ and ‘<’ are represented  |
|               | in XML as ‘&gt;’ ‘&lt;’, respectively.   |
|               | If results and units cannot be split,    |
|               | F.r.3.4 should be used.                  |
+---------------+------------------------------------------+