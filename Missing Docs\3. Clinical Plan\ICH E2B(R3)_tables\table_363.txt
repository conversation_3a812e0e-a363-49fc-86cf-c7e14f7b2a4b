+---------------+------------------------------------------+
| User Guidance | This data element captures a code to     |
|               | inform the organisation that submitted   |
|               | the ICH ICSR message whether to (i) ICSR |
|               | message successfully loaded, or (ii) the |
|               | ICSR message contains fatal error that   |
|               | prevents the ICSR from being loaded.     |
+---------------+------------------------------------------+
| Conformance   | Required                                 |
+---------------+------------------------------------------+
| Data Type     | 2A                                       |
+---------------+------------------------------------------+
| OID           | None                                     |
+---------------+------------------------------------------+
| Value Allowed | CA – Commit Accept (the ICSR message     |
|               | successfully loaded) CR – Commit         |
|               | Reject(the ICSR message contains fatal   |
|               | error that prevents the ICSR from being  |
|               | loaded)                                  |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               |                                          |
+---------------+------------------------------------------+