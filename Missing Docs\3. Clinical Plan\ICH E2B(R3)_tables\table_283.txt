+---------------+------------------------------------------+
| User Guidance | This data element captures the UCUM code |
|               | that best describes the unit for the     |
|               | dosing time interval (G.k.4.r.2).When a  |
|               | specific time interval for drug          |
|               | administration is not known, but is      |
|               | confirmed that the drug is used          |
|               | cyclically or as necessary, then         |
|               | ‘Cyclical’ or ‘As Necessary’ can be used |
|               | in this data element. When the total     |
|               | amount of a drug is provided without any |
|               | particular dose and dosing interval      |
|               | (e.g. in the case of an overdose), the   |
|               | quantity and unit (G.k.4.r.1a and        |
|               | G.k.4.r.1b) is provided along with       |
|               | ‘Total’ in this data element (G.k.4.r.2  |
|               | is left blank).                          |
+---------------+------------------------------------------+
| Conformance   | Optional, but required if G.k.4.r.2 is   |
|               | populated.                               |
+---------------+------------------------------------------+
| Data Type     | 50AN                                     |
+---------------+------------------------------------------+
| OID           | 2.16.840.1.113883.3.989.2.1.1.26         |
+---------------+------------------------------------------+
| Value Allowed | Constrained UCUM codes:{cyclical},       |
|               | {asnecessary},{total}                    |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | UCUM allows using non-unit expression    |
|               | for symbols not in UCUM. In this case,   |
|               | {cyclical}, {as necessary}, and {total}  |
|               | can be used in XML message.              |
+---------------+------------------------------------------+