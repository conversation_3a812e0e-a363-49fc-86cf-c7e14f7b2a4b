+------------+------------+
| Term       | Definition |
+------------+------------+
|            | other orga |
|            | nisation   |
|            | that       |
|            | describes  |
|            | an adverse |
|            | drug       |
|            | reaction   |
|            | in a       |
|            | patient    |
|            | given one  |
|            | or more    |
|            | medicinal  |
|            | products   |
|            | and which  |
|            | does not   |
|            | derive     |
|            | from a     |
|            | study or   |
|            | any        |
|            | organized  |
|            | data       |
|            | collection |
|            | scheme.[IC |
|            | H E2C(R1)] |
+------------+------------+
| Standard   | A          |
|            | technical  |
|            | specificat |
|            | ion which  |
|            | addresses  |
|            | a business |
|            | requiremen |
|            | t, has     |
|            | been imple |
|            | mented in  |
|            | viable     |
|            | commercial |
|            | products,  |
|            | and, to    |
|            | the extent |
|            | practical, |
|            | complies   |
|            | with       |
|            | recognised |
|            | standards  |
|            | organisati |
|            | ons such   |
|            | as ISO.    |
+------------+------------+
| Use Case   | A descript |
|            | ion of a   |
|            | system's   |
|            | behaviour  |
|            | as it      |
|            | responds   |
|            | to a       |
|            | request    |
|            | that       |
|            | originates |
|            | from       |
|            | outside of |
|            | that syste |
|            | m.[Objecto |
|            | ry AB]     |
+------------+------------+