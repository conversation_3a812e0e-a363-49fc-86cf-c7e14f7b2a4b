+---------------+------------------------------------------+
| User Guidance | The HL7 datatypes, which are used within |
|               | the definition of all model elements,    |
|               | are defined within the two schemas,      |
|               | datatypes-base, and datatypes.           |
|               | datatypes-baseschema defines data types  |
|               | for both complex type (e.g. ED, CD) and  |
|               | simple type (e.g. ST, CS). HL7 data      |
|               | types are categorised into ‘Basic data   |
|               | type’ and ‘Generic data type’. This      |
|               | schema defines Basic data type and part  |
|               | of Generic data type. Basic data type is |
|               | a combination of Generic data types and  |
|               | such components are included in this     |
|               | schema.                                  |
+---------------+------------------------------------------+