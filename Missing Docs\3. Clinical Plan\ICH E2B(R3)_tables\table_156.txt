+---------------+------------------------------------------+
| User Guidance | This data element captures information   |
|               | about any other medical history that     |
|               | could not be coded in D.7.1.r. Also, the |
|               | term ‘None’ should be used here when     |
|               | there is no relevant medical history and |
|               | no concurrent conditions reported. If    |
|               | relevant medical history is not          |
|               | documented at the time of the report,    |
|               | this data element is set to unknown      |
|               | (i.e. nullFlavor=UNK) and should not be  |
|               | confused with ‘None’.                    |
+---------------+------------------------------------------+
| Conformance   | Optional, but required if Section D.7.1  |
|               | is null.                                 |
+---------------+------------------------------------------+
| Data Type     | 10000AN                                  |
+---------------+------------------------------------------+
| OID           | None                                     |
+---------------+------------------------------------------+
| Value Allowed | Free text nullFlavor: MSK, ASKU, NASK,   |
|               | UNK                                      |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | If the relevant medical history is       |
|               | unknown to the sender (e.g. not          |
|               | reported), this data element should be   |
|               | left blank with nullFlavor = UNK. Please |
|               | see Section 3.3.6 for further guidance   |
|               | on the use of nullFlavor to describe     |
|               | missing or non-transmitted information.  |
+---------------+------------------------------------------+