+---------------+------------------------------------------+
|            TABLE CONTINUED FROM PREVIOUS PAGE           |
+---------------+------------------------------------------+
| Conformance   | Required                                 |
+---------------+------------------------------------------+
| Data Type     | 60AN                                     |
+---------------+------------------------------------------+
| OID           | 2.16.840.1.113883.3.989.2.1.3.18         |
+---------------+------------------------------------------+
| Value Allowed | Free text                                |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | This should be the same identifier as    |
|               | N.1.3. The following notation will be    |
|               | used to represent ACK.M.3: <id           |
|               | extension="acknowledgement receiver      |
|               | identifier" root="2.16.840.1.113883.3.98 |
|               | 9.2.1.3.18"/> The root indicates the     |
|               | namespace of ACK.M.3, the actual batch   |
|               | receiver identifier is populated at id   |
|               | extension. The sender identifier should  |
|               | be agreed between trading partners.      |
+---------------+------------------------------------------+
| User Guidance | This data element captures the date on   |
|               | which the ICH ICSR Acknowledgement batch |
|               | file was transmitted.                    |
+---------------+------------------------------------------+
| Conformance   | Required                                 |
+---------------+------------------------------------------+
| Data Type     | Date / Time                              |
+---------------+------------------------------------------+
| OID           | None                                     |
+---------------+------------------------------------------+
| Value Allowed | See Appendix II for further information. |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | Minimum precision required is date and   |
|               | time to the second. The date specified   |
|               | cannot refer to a future date; the       |
|               | timezone may have to be specified. (i.e. |
|               | ‘CCYYMMDDhhmmss[+/-ZZzz]’)               |
+---------------+------------------------------------------+