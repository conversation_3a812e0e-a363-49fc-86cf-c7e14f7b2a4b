+---------------+------------------------------------------+
| User Guidance | This data element captures the country   |
|               | where the reaction occurred. For example |
|               | a patient living in Country A            |
|               | experienced headache while travelling in |
|               | Country B; this headache was suspected   |
|               | to be an adverse drug reaction and was   |
|               | reported by a health professional in     |
|               | Country C. The data element C.2.r.3      |
|               | should be populated with Country C, and  |
|               | the data element E.i.9 should be         |
|               | populated with Country B.                |
+---------------+------------------------------------------+
| Conformance   | Optional                                 |
+---------------+------------------------------------------+
| Data Type     | 2A                                       |
+---------------+------------------------------------------+
| OID           | 1.0.3166.1.2.2                           |
+---------------+------------------------------------------+
| Value Allowed | ISO 3166-1 alpha-2, EU                   |
+---------------+------------------------------------------+
| Business      |                                          |
| Rule(s)       |                                          |
+---------------+------------------------------------------+
|               | A two character country code will be     |
|               | used in all instances. The country code  |
|               | EU exists in the ISO 3166 country code   |
|               | list as an exceptional reservation code  |
|               | to support any application that needs to |
|               | represent the name European Union.In     |
|               | this case, ‘EU’ will be accepted as the  |
|               | country code.                            |
+---------------+------------------------------------------+